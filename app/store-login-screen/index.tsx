import React from "react";
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import { ArrowLeft } from "lucide-react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import Button from "@/components/common/Button";
import TextComponent from "@/components/common/Text";
import SectionTitle from "@/components/common/SectionTitle";
import ControlledInput from "@/components/common/ControlledInput";
import { useForm } from "react-hook-form";
import { styled } from "styled-components/native";
import { useShopifyStoreLoginByEmail } from "@/methods/outfits";

// Styled Header to match `StoreListScreen`
const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 16px;
  padding-bottom: 12px;
  background-color: #ffffff;
`;

export default function StoreLoginScreen() {
  const router = useRouter();
  const { storeName = "Store", storeId } = useLocalSearchParams();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({ defaultValues: { email: "" } });

  const loginMutation = useShopifyStoreLoginByEmail();

  const onSubmit = (data: any) => {
    loginMutation.mutate(
      { storeId, email: data.email },
      {
        onSuccess: (res) => {
          console.log("Login success:", res);
          // maybe redirect?
        },
        onError: (err) => {
          console.error("Login failed:", err);
        },
      }
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />

      {/* Shared styled header */}
      <Header>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ArrowLeft size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Login to {storeName}</Text>
      </Header>

      {/* Main content */}
      <View style={styles.body}>
        <View style={styles.logoBox}>
          {/* <Image source={require('@/assets/images/skims.png')} style={styles.logo} resizeMode="contain" /> */}
        </View>

        <TextComponent
          string={`It looks like your Myuse email doesn’t match your ${storeName} account.`}
        />
        <TextComponent
          string={`Enter the email you use with ${storeName} to continue.`}
          style={styles.description}
        />

        <Text style={styles.label}>Email</Text>

        <ControlledInput
          control={control}
          name="email"
          placeholder="<EMAIL>"
          rules={{ required: "Email is required" }}
          error={errors?.email?.message as string}
        />

        <Text style={styles.helperText}>
          We’ll check if you have an account with this store
        </Text>

        <Button
          title="Continue"
          onPress={handleSubmit(onSubmit)}
          isDisabled={!isValid}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backButton: {
    paddingRight: 12,
    paddingTop: 2,
    marginRight: 24,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1A3B34",
  },
  body: {
    paddingHorizontal: 24,
    gap: 16,
    marginTop: 12,
  },
  logoBox: {
    height: 120,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FAFAFA",
    borderRadius: 16,
    marginVertical: 16,
  },
  logo: {
    width: 140,
    height: 60,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    color: "#444",
  },
  label: {
    marginTop: 16,
    fontWeight: "600",
    fontSize: 14,
    color: "#333",
  },
  helperText: {
    textAlign: "center",
    color: "#888",
    fontSize: 12,
    marginTop: 12,
  },
});
