import React, { useState, useMemo, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { ArrowLeft, Search } from "lucide-react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import styled from "styled-components/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { PADDING } from "@/constants/responsive";
import { SearchContainer, SearchInput } from "../closet/PlanOutfitModal.styles";
import { Field, FieldGroup } from "@/components/common/Field";
import { DateTouchable } from "@/components/CreateTripComponent/styles";

const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: ${PADDING.CONTAINER_HORIZONTAL}px;
  padding-top: 16px;
  padding-bottom: 12px;
  background-color: #ffffff;
`;

const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
`;

const ActionRow = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
`;

const ColumnToggle = styled.TouchableOpacity<{ active?: boolean }>`
  padding: 6px 10px;
  border-width: 1px;
  border-color: ${({ active }) => (active ? "#0E7E61" : "#ccc")};
  background-color: ${({ active }) => (active ? "#0E7E61" : "#fff")};
  border-radius: 8px;
  margin-left: 6px;
`;

const ProductCard = styled.TouchableOpacity<{ columns: number }>`
  flex: 1;
  max-width: ${({ columns }) => `${100 / columns - 2}%`};
  margin: 4px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  border-width: 1px;
  border-color: #eee;
`;

const Checkbox = styled.TouchableOpacity<{ checked: boolean }>`
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  background-color: ${({ checked }) => (checked ? "#0E7E61" : "#fff")};
  border-width: 1px;
  border-color: #0e7e61;
`;

const YourOrdersScreen = () => {
  const router = useRouter();
  const { store } = useLocalSearchParams();

  const storeData = store ? JSON.parse(store as string) : null;
  const storeId = storeData?.data?.store?.id || "default";
  const CACHE_KEY = `your-orders-cache-${storeId}`;

  // State
  const [orders, setOrders] = useState<any[]>(storeData?.data?.orders || []);
  const [searchQuery, setSearchQuery] = useState("");
  const [columns, setColumns] = useState<2 | 3>(3);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [loadingCache, setLoadingCache] = useState(true);

  // --- Cache Load ---
  useEffect(() => {
    console.log("[CACHE] loadCache() — key:", CACHE_KEY);
    const loadCache = async () => {
      try {
        const raw = await AsyncStorage.getItem(CACHE_KEY);
        if (!raw) {
          console.log("[CACHE] no cache found for", CACHE_KEY);
          setLoadingCache(false);
          return;
        }

        const parsed = JSON.parse(raw);
        console.log("[CACHE] parsed cache object:", parsed);

        if (parsed.searchQuery !== undefined) setSearchQuery(parsed.searchQuery);
        if (Array.isArray(parsed.selectedItems)) setSelectedItems(parsed.selectedItems);
        if (parsed.columns) setColumns(parsed.columns);
        if (Array.isArray(parsed.orders) && parsed.orders.length > 0) {
          setOrders(parsed.orders);
        }

        console.log("[CACHE] load complete. loadingCache -> false");
      } catch (err) {
        console.error("[CACHE] load error:", err);
      } finally {
        setLoadingCache(false);
      }
    };

    loadCache();
  }, [CACHE_KEY]);

  // --- Cache Save ---
  useEffect(() => {
    const saveCache = async () => {
      if (!orders || orders.length === 0) {
        console.log("[CACHE] skip save — orders invalid");
        return;
      }

      const payload = { searchQuery, selectedItems, columns, orders };
      try {
        console.log("[CACHE] saveCache() — key:", CACHE_KEY, "payload:", payload);
        await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(payload));
        console.log("[CACHE] save success for key:", CACHE_KEY);
      } catch (err) {
        console.error("[CACHE] save error:", err);
      }
    };

    if (!loadingCache) saveCache();
  }, [searchQuery, selectedItems, columns, orders, CACHE_KEY, loadingCache]);

  // --- Filtering ---
  const filteredProducts = useMemo(() => {
    const filtered = orders.filter((item: any) =>
      item.productName?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    console.log(
      "[DATA] filteredProducts computed. filter:",
      searchQuery,
      "->",
      filtered.length,
      "items"
    );
    return filtered;
  }, [orders, searchQuery]);

  // --- Toggle Selection ---
  const toggleSelection = (id: string) => {
    setSelectedItems((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]
    );
  };

  return (
    <SafeArea>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />

      <Header>
        <TouchableOpacity
          onPress={() => router.back()}
          activeOpacity={0.7}
          style={styles.backButton}
        >
          <ArrowLeft size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          Your {storeData?.data?.store?.name || "Store"} Orders
        </Text>
      </Header>

      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingBottom: 40,
        }}
      >
        {/* Search */}
        <SearchContainer>
          <Search size={16} color="#767676" />
          <SearchInput
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#767676"
          />
        </SearchContainer>

        {/* Date Filters */}
        <View style={{ marginTop: 20 }}>
          <FieldGroup>
            <Field label="Start Date">
              <DateTouchable activeOpacity={0.8}>
                <Text style={styles.dateText}>Select Start Date</Text>
              </DateTouchable>
            </Field>
            <Field label="End Date">
              <DateTouchable activeOpacity={0.8}>
                <Text style={styles.dateText}>Select End Date</Text>
              </DateTouchable>
            </Field>
          </FieldGroup>
        </View>

        {/* Actions */}
        <ActionRow style={{ marginTop: 20 }}>
          <Text style={{ fontWeight: "600" }}>
            {selectedItems.length} Products Selected
          </Text>
          {selectedItems.length > 0 && (
            <TouchableOpacity style={styles.addButton}>
              <Text style={{ color: "#fff", fontWeight: "600" }}>
                Add to Myuse Closet
              </Text>
            </TouchableOpacity>
          )}
        </ActionRow>

        {/* Column Toggle */}
        <View style={{ flexDirection: "row", marginBottom: 16 }}>
          <ColumnToggle active={columns === 3} onPress={() => setColumns(3)}>
            <Text style={{ color: columns === 3 ? "#fff" : "#0E7E61" }}>
              3 Column
            </Text>
          </ColumnToggle>
          <ColumnToggle active={columns === 2} onPress={() => setColumns(2)}>
            <Text style={{ color: columns === 2 ? "#fff" : "#0E7E61" }}>
              2 Column
            </Text>
          </ColumnToggle>
        </View>

        {/* Orders Grid */}
        {loadingCache ? (
          <ActivityIndicator
            size="large"
            color="#0E7E61"
            style={{ marginTop: 40 }}
          />
        ) : (
          <FlatList
            data={filteredProducts}
            key={columns}
            numColumns={columns}
            keyExtractor={(item) => item.orderId.toString()}
            renderItem={({ item }: any) => (
              <ProductCard
                columns={columns}
                onPress={() =>
                  router.push({
                    pathname: "/product-details",
                    params: {
                      id: item.id,
                      name: item.variantTitle,
                      imageUrl: item.image,
                      size: item.size ?? "N/A",
                      color: item.color ?? "N/A",
                      brand: item.productName,
                      purchaseDate: item.createdAt,
                      price: item.price,
                      inCloset: item.inCloset,
                    },
                  })
                }
              >
                <Image
                  source={
                    item.image
                      ? { uri: item.image }
                      : require("@/assets/images/placeholder-item.png")
                  }
                  style={{ width: "100%", height: 100 }}
                />
                <Text numberOfLines={1} style={styles.productName}>
                  {item.productName}
                </Text>
                <Checkbox
                  checked={selectedItems.includes(item.orderId.toString())}
                  onPress={() => toggleSelection(item.orderId.toString())}
                />
              </ProductCard>
            )}
          />
        )}
      </ScrollView>
    </SafeArea>
  );
};

export default YourOrdersScreen;

const styles = StyleSheet.create({
  backButton: { paddingRight: 12, paddingTop: 2 },
  headerTitle: { fontSize: 20, fontWeight: "700", color: "#1A3B34" },
  addButton: {
    backgroundColor: "#0E7E61",
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 8,
  },
  productName: { padding: 8, fontSize: 13, fontWeight: "500" },
  dateText: {
    color: "#fff",
    fontFamily: "MuktaVaani",
    fontSize: 16,
    textAlign: "center",
  },
});
