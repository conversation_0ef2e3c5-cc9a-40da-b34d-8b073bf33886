import {
  View,
  Text,
  StyleSheet,
  Image,
  StatusBar,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { ArrowLeft } from "lucide-react-native";
import { useRouter, Stack } from "expo-router";
import Button from "@/components/common/Button";
import { PADDING } from "@/constants/responsive";
import { styled } from "styled-components";
import { connectPurchasesMutation, useGetShopifyStores } from "@/methods/outfits";

const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: ${PADDING.CONTAINER_HORIZONTAL}px;
  padding-top: 16px;
  padding-bottom: 12px;
  background-color: #ffffff;
`;

const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
  padding-top: 0;
`;

const ConnectPurchasesScreen = () => {
  const router = useRouter();

  const getStoresMutation = useGetShopifyStores();

  const handleStart = () => {
  router.push("/(tabs)/store-list");
};

  return (
    <SafeArea>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />

      {/* Header */}
      <Header>
        <TouchableOpacity
          onPress={() => router.replace("/(tabs)/closet")}
          activeOpacity={0.7}
          style={styles.backButton}
        >
          <ArrowLeft size={28} color="#333" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Connect Purchases</Text>
      </Header>

      {/* Scrollable content */}
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Image */}
        <Image
          source={require("@/assets/images/closet/connect-cover.jpg")}
          style={styles.image}
        />

        {/* Text */}
        <View style={styles.textBlock}>
          <Text style={styles.heading}>Connect your{"\n"}Past Purchases</Text>
          <Text style={styles.description}>
            Automatically add items you've{"\n"}bought online to your Myuse
            closet
          </Text>
        </View>

        <Button title="Get Started" onPress={handleStart} />
      </ScrollView>
    </SafeArea>
  );
};

export default ConnectPurchasesScreen;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  backButton: {
    paddingRight: 12,
    paddingTop: 2,
    marginRight: 24,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1A3B34",
  },
  image: {
    width: "100%",
    height: 220,
    borderRadius: 12,
    marginBottom: 20,
    marginTop: 20,
  },
  textBlock: {
    marginBottom: 40,
  },
  heading: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1A3B34",
    marginBottom: 32,
  },
  description: {
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
  },
  titleContainer: {
    flex: 1,
    alignItems: "center",
  },
  titleText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 12,
  },
});
