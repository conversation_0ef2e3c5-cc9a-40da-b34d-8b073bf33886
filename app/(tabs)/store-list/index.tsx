import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TextInput,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
} from "react-native";
import { Search, ArrowLeft } from "lucide-react-native";
import { useRouter, Stack } from "expo-router";
import { styled } from "styled-components";
import { SafeAreaView } from "react-native-safe-area-context";
import { useGetShopifyStores, useSelectShopifyStore } from "@/methods/outfits";

const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: 20px;
  padding-top: 16px;
  padding-bottom: 12px;
  background-color: #ffffff;
`;

const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
  padding-top: 0;
`;

const StoreListScreen = () => {
  const router = useRouter();
  const getStoresMutation = useGetShopifyStores();
  const [search, setSearch] = useState("");
  const [viewMore, setViewMore] = useState(false);

  useEffect(() => {
    getStoresMutation.mutateAsync().catch(console.error);
  }, []);

  const isLoading = getStoresMutation.isPending;
  const isError = getStoresMutation.isError;
  const stores = getStoresMutation.data?.data?.stores || [];

  useEffect(() => {
    console.log("stores: ", stores);
  }, [stores]);

  const filteredStores = stores.filter((store) =>
    store.name.toLowerCase().includes(search.toLowerCase())
  );

  const storesToShow = viewMore ? filteredStores : filteredStores.slice(0, 10);

  const selectStoreMutation = useSelectShopifyStore();
  const [selectedStoreId, setSelectedStoreId] = useState<string | null>(null);

  const handleSelectStore = async (storeId: string) => {
    try {
      setSelectedStoreId(storeId);
      const result = await selectStoreMutation.mutateAsync({ storeId });

      if (!result.success && result.error === "not-found") {
        // redirect to login screen with store info
        router.push({
          pathname: "/store-login-screen",
          params: {
            storeId,
            storeName: stores.find((s) => s.id === storeId)?.name || "Store",
            // You can pass logoUrl if available
          },
        });
        return;
      }

      console.log("Store selected:", result);
      router.push({
        pathname: "/your-orders-screen",
        params: { store: JSON.stringify(result) },
      });
    } catch (error) {
      console.error("Failed to select store:", error);
    } finally {
      setSelectedStoreId(null);
    }
  };

  return (
    <SafeArea>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_bottom",
        }}
      />

      {/* Header */}
      <Header>
        <TouchableOpacity
          onPress={() => router.replace("/(tabs)/connect-purchases-screen")}
          activeOpacity={0.7}
          style={styles.backButton}
        >
          <ArrowLeft size={28} color="#333" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Select a Store</Text>
      </Header>

      {/* Subtitle */}
      <Text style={styles.subtitle}>
        Choose a store to connect{"\n"}your online purchases
      </Text>

      {/* Search */}
      {filteredStores.length > 10 && (
        <View style={styles.searchWrapper}>
          <Search size={18} color="#888" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search stores..."
            value={search}
            onChangeText={setSearch}
            placeholderTextColor="#999"
          />
        </View>
      )}

      {/* Loader */}
      {isLoading && (
        <ActivityIndicator
          size="large"
          color="#1A3B34"
          style={{ marginTop: 20 }}
        />
      )}

      {/* Error */}
      {isError && (
        <Text style={styles.error}>
          We could not find any products associated with this account. Please
          try again.
        </Text>
      )}

      {/* Store List */}
      <FlatList
        data={storesToShow}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        renderItem={({ item }) => {
          const isPending =
            selectedStoreId === item.id && selectStoreMutation.isPending;

          const StoreContent = (
            <View style={styles.storeItem}>
              <View style={styles.storeInfo}>
                <View style={styles.logoPlaceholder}>
                  <Text style={styles.logoText}>{item.name[0]}</Text>
                </View>
                <View>
                  <Text style={styles.storeName}>{item.name}</Text>
                  <Text style={styles.storeCategory}>
                    Clothing & Accessories
                  </Text>
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.connectButton,
                  item.isConnected && styles.connectedButton,
                ]}
                onPress={() => handleSelectStore(item.id)}
                disabled={isPending}
              >
                {isPending ? (
                  <ActivityIndicator color="#ffffff" size="small" />
                ) : (
                  <Text
                    style={[
                      styles.connectText,
                      item.isConnected && styles.connectedText,
                    ]}
                  >
                    {item.isConnected ? "Connected" : "Connect"}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          );

          // If connected, make entire row clickable
          if (item.isConnected) {
            return (
              <TouchableOpacity
                activeOpacity={0.7}
                onPress={() => handleSelectStore(item.id)}
              >
                {StoreContent}
              </TouchableOpacity>
            );
          }

          // If not connected, return normal non-clickable row
          return StoreContent;
        }}
        ListEmptyComponent={
          !isLoading && <Text style={styles.empty}>No stores found.</Text>
        }
        ListFooterComponent={
          !viewMore && filteredStores.length > 10 ? (
            <TouchableOpacity
              onPress={() => setViewMore(true)}
              style={styles.viewMore}
            >
              <Text style={styles.viewMoreText}>View More</Text>
            </TouchableOpacity>
          ) : null
        }
      />
    </SafeArea>
  );
};

export default StoreListScreen;

const styles = StyleSheet.create({
  backButton: {
    paddingRight: 12,
    paddingTop: 2,
    marginRight: 24,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1A3B34",
  },
  subtitle: {
    fontSize: 18,
    color: "#666",
    marginBottom: 16,
    paddingHorizontal: 20,
    lineHeight: 20,
    textAlign: "center",
    alignSelf: "center",
  },
  storeItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomColor: "#eee",
    borderBottomWidth: 1,
  },
  storeInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  logoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#eee",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  logoText: {
    fontSize: 16,
    color: "#333",
    fontWeight: "700",
  },
  storeName: {
    fontSize: 16,
    color: "#1A3B34",
    fontWeight: "600",
  },
  storeCategory: {
    fontSize: 12,
    color: "#666",
  },
  connectButton: {
    borderWidth: 1,
    borderColor: "#007B5C",
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
  },
  connectText: {
    fontSize: 13,
    color: "#007B5C",
    fontWeight: "600",
  },
  connectedButton: {
    backgroundColor: "#007B5C",
  },
  connectedText: {
    color: "#fff",
  },
  error: {
    color: "red",
    marginTop: 10,
    paddingHorizontal: 20,
  },
  empty: {
    textAlign: "center",
    marginTop: 20,
    color: "#888",
  },
  viewMore: {
    alignItems: "center",
    marginTop: 20,
  },
  viewMoreText: {
    fontSize: 14,
    color: "#007B5C",
    fontWeight: "500",
  },
  scrollContainer: {
    paddingBottom: 40,
  },
  searchWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 12,
    paddingHorizontal: 12,
    marginHorizontal: 20,
    marginBottom: 16,
    backgroundColor: "#fff",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    paddingVertical: 10,
    color: "#000",
  },
});
