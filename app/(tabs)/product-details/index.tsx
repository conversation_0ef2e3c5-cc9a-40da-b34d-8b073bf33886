import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  StatusBar,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import Modal from "react-native-modal";
import { ArrowLeft } from "lucide-react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import { styled } from "styled-components";
import { PADDING } from "@/constants/responsive";
import { addItem } from "@/methods/cloths";

const Header = styled(View)`
  flex-direction: row;
  align-items: center;
  padding: ${PADDING.CONTAINER_HORIZONTAL}px;
  padding-top: 16px;
  padding-bottom: 12px;
  background-color: #ffffff;
`;

const SafeArea = styled(SafeAreaView)`
  flex: 1;
  background-color: #fff;
  padding-top: 0;
`;

export default function ProductDetailsScreen() {
  const router = useRouter();
  const { name, imageUrl, size, color, brand, purchaseDate, price, inCloset } =
    useLocalSearchParams();

  const addItemMutation = addItem();
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleAddToCloset = async () => {
    try {
      const itemData = {
        name,
        size,
        color,
        brand,
        purchaseDate: purchaseDate ? new Date(purchaseDate) : null,
        price: Number(price),
        imageUrl,
        addedMethod: "onlineStore",
      };

      const result = await addItemMutation.mutateAsync(itemData);

      if (result?.itemId) {
        setShowSuccessModal(true);
      } else {
        console.warn("❌ Failed to add item:", result?.message || "Unknown error");
      }
    } catch (err) {
      console.error("🚨 Error adding item:", err);
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    router.replace("/(tabs)/closet");
  };

  return (
    <SafeArea>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
          animation: "slide_from_right",
        }}
      />

      {/* Header */}
      <Header>
        <TouchableOpacity
          onPress={() => router.push(('/(tabs)/your-orders-screen'))}
          activeOpacity={0.7}
          style={styles.backButton}
        >
          <ArrowLeft size={28} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{name}</Text>
      </Header>

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Product Image */}
        <Image
          source={
            imageUrl
              ? { uri: imageUrl }
              : require("@/assets/images/placeholder-item.png")
          }
          style={styles.image}
        />

        {/* Add to Closet Button */}
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddToCloset}
          disabled={addItemMutation.status === "pending"}
        >
          <Text style={styles.addButtonText}>
            {addItemMutation.status === "pending"
              ? "Adding..."
              : inCloset
              ? "Remove from Myuse Closet"
              : "Add to Myuse Closet"}
          </Text>
        </TouchableOpacity>

        {/* Purchase Details */}
        <View style={styles.table}>
          {[
            { label: "Size", value: size },
            { label: "Color", value: color },
            { label: "Brand", value: brand },
            { label: "Purchase Date", value: purchaseDate },
            { label: "Price", value: `$${price}` },
            {
              label: "Status",
              value: inCloset ? "In Myuse Closet" : "Not in Myuse Closet",
            },
          ].map((row, idx) => (
            <View
              key={idx}
              style={[styles.row, idx < 5 ? styles.rowBorder : null]}
            >
              <Text style={styles.cellLabel}>{row.label}:</Text>
              <Text style={styles.cellValue}>{row.value}</Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Success Modal */}
      <Modal
        isVisible={showSuccessModal}
        backdropOpacity={0.5}
        animationIn="fadeInUp"
        animationOut="fadeOutDown"
        onBackdropPress={handleModalClose}
        useNativeDriver
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>
            Successfully added items{"\n"}to your Myuse Closet!
          </Text>
          <Text style={styles.modalSubtitle}>
            You can add them to your Upcoming trips.
          </Text>
          {/* <Image
            source={require("@/assets/images/add-to-closet-success.png")}
            style={styles.modalImage}
            resizeMode="contain"
          /> */}
          <TouchableOpacity style={styles.doneButton} onPress={handleModalClose}>
            <Text style={styles.doneText}>Done</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </SafeArea>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  backButton: {
    paddingRight: 12,
    paddingTop: 2,
    marginRight: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1A3B34",
  },
  image: {
    width: "100%",
    height: 300,
    borderRadius: 12,
    marginBottom: 20,
    marginTop: 20,
  },
  addButton: {
    backgroundColor: "#0E7E61",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 24,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  table: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    overflow: "hidden",
  },
  row: {
    flexDirection: "row",
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  rowBorder: {
    borderBottomWidth: 1,
    borderColor: "#ddd",
  },
  cellLabel: {
    flex: 1,
    fontWeight: "600",
    color: "#333",
  },
  cellValue: {
    flex: 1,
    color: "#555",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 16,
    paddingVertical: 24,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#000",
    textAlign: "center",
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    color: "#555",
    textAlign: "center",
    marginBottom: 24,
  },
  modalImage: {
    width: 180,
    height: 180,
    marginBottom: 32,
  },
  doneButton: {
    backgroundColor: "#007B5C",
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 30,
  },
  doneText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});
