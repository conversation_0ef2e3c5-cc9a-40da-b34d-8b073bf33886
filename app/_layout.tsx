import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import "react-native-reanimated";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import { OverlayProvider } from "@gluestack-ui/overlay";
import { EarnedRewardsWatcher } from "@/components/EarnedRewardsWatcher";
import { EarnedRewardsModalProvider } from "@/context/EarnedRewardsContext";
import { EarnedRewardsModal } from "@/components/EarnedRewardsModal";

import { darkTheme, lightTheme } from "@/constants/Themes";

import { ThemeProvider as StyledThemeProvider } from "styled-components/native";

import { useColorScheme } from "@/components/useColorScheme";

import { DDPConnectionProvider } from "@/context/meteor-context";
import type { NativeStackNavigationOptions } from "@react-navigation/native-stack";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { StatusBar } from "expo-status-bar";

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "/splash-screen/index",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    CormorantGaramond: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-Regular.ttf"),
    CormorantGaramondItalic: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-Italic.ttf"),
    CormorantGaramondBold: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-Bold.ttf"),
    CormorantGaramondBoldItalic: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-BoldItalic.ttf"),
    CormorantGaramondMedium: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-Medium.ttf"),
    CormorantGaramondMediumItalic: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-MediumItalic.ttf"),
    CormorantGaramondSemiBold: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-SemiBold.ttf"),
    CormorantGaramondSemiBoldItalic: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-SemiBoldItalic.ttf"),
    CormorantGaramondLight: require("../assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-Light.ttf"),
    MuktaVaani: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-Regular.ttf"),
    MuktaVaaniBold: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-Bold.ttf"),
    MuktaVaaniMedium: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-Medium.ttf"),
    MuktaVaaniSemiBold: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-SemiBold.ttf"),
    MuktaVaaniLight: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-Light.ttf"),
    MuktaVaaniExtraBold: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-ExtraBold.ttf"),
    MuktaVaaniExtraLight: require("../assets/fonts/Mukta-Vaani-Font/MuktaVaani-ExtraLight.ttf"),
    ...FontAwesome.font,
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  const theme = colorScheme === "dark" ? darkTheme : lightTheme;

  // Define common screen options
  const defaultScreenOptions: NativeStackNavigationOptions = {
    headerShown: false,
    animation: "none",
  } as const;

  const slideFromRightScreenOptions: NativeStackNavigationOptions = {
    ...defaultScreenOptions,
    animation: "slide_from_right",
  } as const;

  const slideFromBottomScreenOptions: NativeStackNavigationOptions = {
    ...defaultScreenOptions,
    animation: "slide_from_bottom",
    contentStyle: { backgroundColor: "#fff" },
  } as const;

  const styleScreenOptions: NativeStackNavigationOptions = {
    ...slideFromRightScreenOptions,
    contentStyle: { backgroundColor: "#fff" },
  } as const;

  return (
    <GluestackUIProvider>
      <OverlayProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <DDPConnectionProvider>
            <EarnedRewardsModalProvider>
              <StatusBar backgroundColor="#fff" style="dark" />
              <StyledThemeProvider theme={theme as any}>
                <EarnedRewardsWatcher />
                <EarnedRewardsModal />
                <Stack>
                  <Stack.Screen name="index" options={defaultScreenOptions} />
                  <Stack.Screen
                    name="splash-screen/index"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="onboarding/index"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="create-trip"
                    options={slideFromBottomScreenOptions}
                  />
                  <Stack.Screen name="sign-up" options={defaultScreenOptions} />
                  <Stack.Screen name="sign-in" options={defaultScreenOptions} />
                  <Stack.Screen
                    name="store-login-screen"
                    options={{
                      // Completely hide the header
                      headerShown: false,
                      // Use card presentation without header
                      presentation: "card",
                      // Use slide animation for smooth transitions
                      animation: "slide_from_right",
                      // Ensure no header is shown
                      header: () => null,
                    }}
                  />
                  <Stack.Screen name="(tabs)" options={defaultScreenOptions} />
                  <Stack.Screen
                    name="rewards"
                    options={slideFromRightScreenOptions}
                  />
                  <Stack.Screen
                    name="style-profile"
                    options={slideFromRightScreenOptions}
                  />
                  <Stack.Screen
                    name="sizing-preferences"
                    options={slideFromRightScreenOptions}
                  />
                  <Stack.Screen
                    name="style-moodboard"
                    options={styleScreenOptions}
                  />
                  <Stack.Screen
                    name="style-keywords"
                    options={styleScreenOptions}
                  />
                  <Stack.Screen
                    name="style-hardpasses"
                    options={styleScreenOptions}
                  />
                  <Stack.Screen
                    name="style-color-match"
                    options={styleScreenOptions}
                  />
                  <Stack.Screen
                    name="trip-overview"
                    options={styleScreenOptions}
                  />
                  <Stack.Screen
                    name="user-info"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="delete-verification"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="delete-confirmed"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="change-username"
                    options={slideFromBottomScreenOptions}
                  />
                  <Stack.Screen
                    name="personal-information"
                    options={slideFromRightScreenOptions}
                  />
                  <Stack.Screen
                    name="modal"
                    options={{ presentation: "modal" }}
                  />
                  <Stack.Screen
                    name="item-details"
                    options={{
                      // Completely hide the header
                      headerShown: false,
                      // Use card presentation without header
                      presentation: "card",
                      // Use slide animation for smooth transitions
                      animation: "slide_from_right",
                      // Ensure no header is shown
                      header: () => null,
                    }}
                  />
                  <Stack.Screen
                    name="forgot-password"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="forgot-password/confirm-identity"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="forgot-password/verification"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="forgot-password/reset-password"
                    options={defaultScreenOptions}
                  />
                  <Stack.Screen
                    name="terms-services"
                    options={slideFromBottomScreenOptions}
                  />
                  <Stack.Screen
                    name="privacy-policy"
                    options={slideFromBottomScreenOptions}
                  />
                  <Stack.Screen
                    name="my-calendar"
                    options={slideFromBottomScreenOptions}
                  />
                  <Stack.Screen
                    name="styling-canvas"
                    options={slideFromBottomScreenOptions}
                  />
                </Stack>
              </StyledThemeProvider>
            </EarnedRewardsModalProvider>
          </DDPConnectionProvider>
        </GestureHandlerRootView>
      </OverlayProvider>
    </GluestackUIProvider>
  );
}
