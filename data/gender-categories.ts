// Gender-specific clothing categories - Now using backend API instead of hardcoded data
import { CategoryItem } from './categories';
import Meteor from '@meteorrn/core';

// Default empty categories (will be populated from API)
export const WOMENS_CATEGORIES: CategoryItem[] = [];
export const MENS_CATEGORIES: CategoryItem[] = [];

// Default categories for fallback
const DEFAULT_CATEGORIES: CategoryItem[] = [
  { id: 'tops', name: 'Tops' },
  { id: 'bottoms', name: 'Bottoms' },
  { id: 'shoes', name: 'Shoes' }
];

// Function to get categories based on gender - now using the backend API
export async function fetchCategoriesFromBackend(gender: string): Promise<CategoryItem[]> {
  try {
    // Map frontend gender values to backend expected values
    // Handle various gender formats: 'Male'/'Female', 'men'/'women', 'male'/'female'
    let backendGender: string | undefined;
    if (gender) {
      const normalizedGender = gender.toLowerCase();
      if (normalizedGender === 'male' || normalizedGender === 'men') {
        backendGender = 'men';
      } else if (normalizedGender === 'female' || normalizedGender === 'women') {
        backendGender = 'women';
      }
    }

    console.log('Fetching categories from backend for gender:', backendGender);

    // Call the backend API to get categories
    const result = await new Promise<any>((resolve, reject) => {
      Meteor.call('itemCategories-fetch', { gender: backendGender }, (err: any, res: any) => {
        if (err) {
          console.error('Error fetching categories from backend:', err);
          reject(err);
          return;
        }

        // Log the full API response structure (without the actual data to keep logs clean)
        console.log('Categories API response structure:',
          Object.keys(res).map(key => `${key}: ${typeof res[key]}`));

        // Log the categories in a more readable format
        if (res?.data?.itemCategories) {
          console.log('\n🔍 DETAILED CATEGORY ANALYSIS 🔍');
          console.log(`Total categories received: ${res.data.itemCategories.length}`);

          // Create a map to track category names for duplicate detection
          const categoryNames = new Map();

          // Log each category with its full structure
          console.log('\n=== MAIN CATEGORIES FROM API ===');
          res.data.itemCategories.forEach((cat: any, index: number) => {
            const catName = cat.name || 'Unnamed';

            // Check for duplicates
            if (categoryNames.has(catName)) {
              categoryNames.set(catName, categoryNames.get(catName) + 1);
            } else {
              categoryNames.set(catName, 1);
            }

            // Log the category with its properties
            console.log(`${index + 1}. ${catName} (ID: ${cat._id || 'No ID'})`);
            console.log(`   Properties: ${Object.keys(cat).join(', ')}`);

            // Log subcategories if they exist
            if (cat.subCategories && cat.subCategories.length > 0) {
              console.log(`   Has ${cat.subCategories.length} subcategories:`);
              cat.subCategories.forEach((subCat: any, subIndex: number) => {
                console.log(`     ${index + 1}.${subIndex + 1} ${subCat.name || 'Unnamed'} (ID: ${subCat._id || 'No ID'})`);
              });
            } else {
              console.log('   No subcategories');
            }
            console.log(''); // Empty line for readability
          });

          // Log duplicate analysis
          console.log('\n=== CATEGORY DUPLICATION ANALYSIS ===');
          let hasDuplicates = false;
          categoryNames.forEach((count, name) => {
            if (count > 1) {
              console.log(`⚠️ DUPLICATE: "${name}" appears ${count} times`);
              hasDuplicates = true;
            }
          });

          if (!hasDuplicates) {
            console.log('✅ No duplicate category names found');
          }

          // Log the unique category names
          console.log('\n=== UNIQUE CATEGORY NAMES ===');
          console.log(Array.from(categoryNames.keys()).join(', '));
          console.log('=== END DETAILED CATEGORY ANALYSIS ===\n');
        }

        resolve(res);
      });
    });

    // Check if we got valid data
    if (result?.success && result.data?.itemCategories && result.data.itemCategories.length > 0) {
      console.log(`Received ${result.data.itemCategories.length} categories from backend`);

      // Create a Map to track categories by name for deduplication
      const categoryMap = new Map<string, any>();

      // First pass: collect categories by name, keeping only the first occurrence
      result.data.itemCategories.forEach((category: any) => {
        // Skip if category is undefined or null
        if (!category) {
          console.log('Skipping undefined or null category');
          return;
        }

        // Skip categories without names - check both name and mainCategory
        if (!category.name && !category.mainCategory) {
          console.log('Skipping category without a name or mainCategory');
          return;
        }

        // Use mainCategory as name if name is not available (current backend schema)
        const categoryName = category.name || category.mainCategory;
        if (!categoryName) {
          console.log('Skipping category without a valid name');
          return;
        }

        // Normalize the category name for case-insensitive comparison
        const normalizedName = categoryName.toLowerCase().trim();

        // Check if we already have this category (case-insensitive)
        if (!categoryMap.has(normalizedName)) {
          categoryMap.set(normalizedName, category);
        } else {
          console.log(`Duplicate category found: ${categoryName} (normalized: ${normalizedName})`);

          // If the current category has subcategories but the stored one doesn't,
          // or if the current one has more subcategories, use the current one instead
          const storedCategory = categoryMap.get(normalizedName);
          // Add null checks
          if (!storedCategory) {
            console.log(`Stored category for ${categoryName} is unexpectedly undefined, using current category`);
            categoryMap.set(normalizedName, category);
            return;
          }

          const currentHasSubcats = category.subCategories && category.subCategories.length > 0;
          const storedHasSubcats = storedCategory.subCategories && storedCategory.subCategories.length > 0;

          if ((currentHasSubcats && !storedHasSubcats) ||
              (currentHasSubcats && storedHasSubcats &&
               category.subCategories.length > storedCategory.subCategories.length)) {
            console.log(`Using category with more subcategories for: ${categoryName}`);
            categoryMap.set(normalizedName, category);
          }
        }
      });

      console.log(`After deduplication: ${categoryMap.size} unique categories`);

      // Map deduplicated categories to our CategoryItem format
      // Filter out any categories without names before mapping
      return Array.from(categoryMap.values())
        .filter((category: any) => category.name || category.mainCategory)
        .map((category: any) => {
          const categoryName = category.name || category.mainCategory;
          const categoryId = category._id || `${backendGender}-${categoryName.toLowerCase().replace(/\s+/g, '-')}`;

        // Create a Map to track subcategories by name for deduplication
        const subCategoryMap = new Map<string, any>();

        // If the category has subcategories, deduplicate them too
        if (category.subCategories && Array.isArray(category.subCategories) && category.subCategories.length > 0) {
          category.subCategories.forEach((subCat: any) => {
            // Skip if subcategory is undefined or null
            if (!subCat) {
              console.log('Skipping undefined or null subcategory');
              return;
            }

            // Skip subcategories without names
            if (!subCat.name) {
              console.log('Skipping subcategory without a name');
              return;
            }

            const subCatName = subCat.name;
            // Normalize subcategory name for case-insensitive comparison
            const normalizedSubCatName = subCatName.toLowerCase().trim();

            if (!subCategoryMap.has(normalizedSubCatName)) {
              subCategoryMap.set(normalizedSubCatName, subCat);
            } else {
              console.log(`Skipping duplicate subcategory: ${subCatName} (normalized: ${normalizedSubCatName})`);
            }
          });
        }

        return {
          id: categoryId,
          name: categoryName,
          // If the category has subcategories, map them too (after deduplication)
          ...(subCategoryMap.size > 0 ? {
            children: Array.from(subCategoryMap.values())
              .filter((subCat: any) => subCat !== null && subCat !== undefined && subCat.name) // Filter out null/undefined and items without names
              .map((subCat: any) => {
                return {
                  id: subCat._id || `${categoryId}-${subCat.name.toLowerCase().replace(/\s+/g, '-')}`,
                  name: subCat.name
                };
              })
          } : {})
        };
      });
    } else {
      console.warn('No categories returned from backend or API call failed');
      return DEFAULT_CATEGORIES;
    }
  } catch (error) {
    console.error('Error in fetchCategoriesFromBackend:', error);
    return DEFAULT_CATEGORIES;
  }
}

// Function to get categories based on gender
export function getCategoriesByGender(_gender: string | null | undefined): CategoryItem[] {
  // For backward compatibility, return default categories
  // Components should be updated to use the async version instead
  console.warn('Using synchronous getCategoriesByGender - consider updating to async fetchCategoriesFromBackend');

  return DEFAULT_CATEGORIES;
}
