import { useMutation } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';

type CategoryAddResponse = {
  success: boolean;
  message?: string;
  error?: any;
};

type SubCategory = {
  name: string;
  description?: string;
};

type AddCategoryParams = {
  description?: string;
  gender: 'men' | 'women';        // Required as per backend schema
  mainCategory: string;           // Required as per backend schema
  subCategories?: SubCategory[];
};

/**
 * Hook to add a new category using the itemCategories-add API endpoint
 * 
 * @returns A mutation function that can be used to add a new category
 * 
 * Example usage:
 * ```
 * const addCategoryMutation = useAddCategory();
 * 
 * // Add a main category
 * addCategoryMutation.mutate({
 *   mainCategory: 'Tech',
 *   description: 'Technology items',
 *   gender: 'men'
 * });
 *
 * // Add a category with subcategories
 * addCategoryMutation.mutate({
 *   mainCategory: 'Accessories',
 *   description: 'Fashion accessories',
 *   gender: 'women',
 *   subCategories: [
 *     { name: 'Watches', description: 'Wrist watches' },
 *     { name: 'Jewelry', description: 'Necklaces, rings, etc.' }
 *   ]
 * });
 * ```
 */
export const useAddCategory = () => 
  useMutation({
    mutationFn: (params: AddCategoryParams) => {
      return new Promise<CategoryAddResponse>((resolve, reject) => {
        console.log('Adding category with params:', JSON.stringify(params, null, 2));
        
        Meteor.call('itemCategories-add', params, (err: any, res: CategoryAddResponse) => {
          if (err) {
            console.error('Error adding category:', err);
            reject(err);
            return;
          }
          
          console.log('Category add response:', JSON.stringify(res, null, 2));
          
          if (res.success) {
            resolve(res);
          } else {
            console.error('Category add failed:', res.error);
            reject(res.error);
          }
        });
      });
    },
  });

/**
 * Helper function to add a main category with subcategories
 * 
 * @param name The name of the main category
 * @param gender The gender for the category ('men' or 'women')
 * @param description Optional description for the category
 * @param subcategories Optional array of subcategory names
 * @returns A promise that resolves when the category is added
 * 
 * Example usage:
 * ```
 * // Add a main category with subcategories
 * addMainCategoryWithSubcategories(
 *   'Accessories', 
 *   'women', 
 *   'Fashion accessories',
 *   ['Watches', 'Jewelry', 'Belts', 'Scarves']
 * ).then(() => {
 *   console.log('Category added successfully');
 * }).catch(error => {
 *   console.error('Failed to add category:', error);
 * });
 * ```
 */
export const addMainCategoryWithSubcategories = async (
  name: string,
  gender: 'men' | 'women',
  description?: string,
  subcategories?: string[]
): Promise<CategoryAddResponse> => {
  return new Promise<CategoryAddResponse>((resolve, reject) => {
    // Prepare subcategories array if provided
    const subCategoriesArray = subcategories?.map(subName => ({
      name: subName,
      description: `${subName} subcategory of ${name}`
    }));
    
    // Prepare params object
    const params: AddCategoryParams = {
      mainCategory: name,
      gender,
      description: description || `${name} category for ${gender === 'men' ? 'men' : 'women'}`
    };
    
    // Add subcategories if provided
    if (subCategoriesArray && subCategoriesArray.length > 0) {
      params.subCategories = subCategoriesArray;
    }
    
    console.log('Adding main category with params:', JSON.stringify(params, null, 2));
    
    // Call the Meteor method
    Meteor.call('itemCategories-add', params, (err: any, res: CategoryAddResponse) => {
      if (err) {
        console.error('Error adding main category:', err);
        reject(err);
        return;
      }
      
      console.log('Main category add response:', JSON.stringify(res, null, 2));
      
      if (res.success) {
        resolve(res);
      } else {
        console.error('Main category add failed:', res.error);
        reject(res.error);
      }
    });
  });
};
