import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useAddCategory } from '@/methods/category-management';

interface AddCategoryFormProps {
  onSuccess?: () => void;
}

export default function AddCategoryForm({ onSuccess }: AddCategoryFormProps) {
  const [categoryName, setCategoryName] = useState('');
  const [categoryDescription, setCategoryDescription] = useState('');
  const [gender, setGender] = useState<'men' | 'women'>('women');
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [newSubcategory, setNewSubcategory] = useState('');

  const addCategoryMutation = useAddCategory();

  const handleAddSubcategory = () => {
    if (newSubcategory.trim()) {
      setSubcategories([...subcategories, newSubcategory.trim()]);
      setNewSubcategory('');
    }
  };

  const handleRemoveSubcategory = (index: number) => {
    const newSubcategories = [...subcategories];
    newSubcategories.splice(index, 1);
    setSubcategories(newSubcategories);
  };

  const handleSubmit = async () => {
    if (!categoryName.trim()) {
      Alert.alert('Error', 'Category name is required');
      return;
    }

    try {
      // Prepare subcategories array
      const subCategoriesArray = subcategories.map((name) => ({
        name,
        description: `${name} subcategory of ${categoryName}`,
      }));

      // Call the mutation
      await addCategoryMutation.mutateAsync({
        mainCategory: categoryName,
        description: categoryDescription || `${categoryName} category`,
        gender,
        subCategories:
          subCategoriesArray.length > 0 ? subCategoriesArray : undefined,
      });

      // Show success message
      Alert.alert('Success', 'Category added successfully');

      // Reset form
      setCategoryName('');
      setCategoryDescription('');
      setSubcategories([]);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error adding category:', error);
      Alert.alert('Error', 'Failed to add category. Please try again.');
    }
  };

  return (
    <ScrollView
      style={styles.container}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <Text style={styles.title}>Add New Category</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Category Name *</Text>
        <TextInput
          style={styles.input}
          value={categoryName}
          onChangeText={setCategoryName}
          placeholder="Enter category name"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Description</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={categoryDescription}
          onChangeText={setCategoryDescription}
          placeholder="Enter category description"
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Gender</Text>
        <View style={styles.radioGroup}>
          <TouchableOpacity
            style={[
              styles.radioButton,
              gender === 'women' && styles.radioButtonSelected,
            ]}
            onPress={() => setGender('women')}
          >
            <Text
              style={[
                styles.radioText,
                gender === 'women' && styles.radioTextSelected,
              ]}
            >
              Women
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.radioButton,
              gender === 'men' && styles.radioButtonSelected,
            ]}
            onPress={() => setGender('men')}
          >
            <Text
              style={[
                styles.radioText,
                gender === 'men' && styles.radioTextSelected,
              ]}
            >
              Men
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Subcategories</Text>
        <View style={styles.subcategoryInputContainer}>
          <TextInput
            style={[styles.input, styles.subcategoryInput]}
            value={newSubcategory}
            onChangeText={setNewSubcategory}
            placeholder="Enter subcategory name"
          />
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddSubcategory}
          >
            <Text style={styles.addButtonText}>Add</Text>
          </TouchableOpacity>
        </View>

        {subcategories.length > 0 && (
          <View style={styles.subcategoriesList}>
            {subcategories.map((subcat, index) => (
              <View key={index} style={styles.subcategoryItem}>
                <Text style={styles.subcategoryText}>{subcat}</Text>
                <TouchableOpacity
                  onPress={() => handleRemoveSubcategory(index)}
                >
                  <Text style={styles.removeButton}>✕</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        )}
      </View>

      <TouchableOpacity
        style={styles.submitButton}
        onPress={handleSubmit}
        disabled={addCategoryMutation.isPending}
      >
        {addCategoryMutation.isPending ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.submitButtonText}>Add Category</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#0E7E61',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  radioButton: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginRight: 12,
    minWidth: 100,
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#0E7E61',
    backgroundColor: '#F0F9F6',
  },
  radioText: {
    fontSize: 16,
    color: '#333',
  },
  radioTextSelected: {
    color: '#0E7E61',
    fontWeight: '500',
  },
  subcategoryInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subcategoryInput: {
    flex: 1,
    marginRight: 8,
  },
  addButton: {
    backgroundColor: '#0E7E61',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  subcategoriesList: {
    marginTop: 12,
  },
  subcategoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F0F9F6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  subcategoryText: {
    fontSize: 16,
  },
  removeButton: {
    color: '#FF6B6B',
    fontSize: 18,
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#0E7E61',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '500',
  },
});
